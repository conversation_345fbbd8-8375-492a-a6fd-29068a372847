#include <iostream>
#include "employee_attendance.h"

void displayMenu() {
    std::cout << "Employee Attendance System\n";
    std::cout << "1. Add Employee\n";
    std::cout << "2. Remove Employee\n";
    std::cout << "3. Update Employee Information\n";
    std::cout << "4. Check-in\n";
    std::cout << "5. Check-out\n";
    std::cout << "6. Search Employee\n";
    std::cout << "7. Display All Employees\n";
    std::cout << "8. Sort Employees\n";
    std::cout << "9. Generate Attendance Report\n";
    std::cout << "10. Exit\n";
    std::cout << "Select an option: ";
}

int main() {
    EmployeeAttendanceSystem attendanceSystem;
    int choice;

    do {
        displayMenu();
        std::cin >> choice;

        switch (choice) {
            case 1:
                attendanceSystem.addEmployee();
                break;
            case 2:
                attendanceSystem.removeEmployee();
                break;
            case 3:
                attendanceSystem.updateEmployee();
                break;
            case 4:
                attendanceSystem.checkIn();
                break;
            case 5:
                attendanceSystem.checkOut();
                break;
            case 6:
                attendanceSystem.searchEmployee();
                break;
            case 7:
                attendanceSystem.displayAllEmployees();
                break;
            case 8:
                attendanceSystem.sortEmployees();
                break;
            case 9:
                attendanceSystem.generateReport();
                break;
            case 10:
                std::cout << "Exiting the system.\n";
                break;
            default:
                std::cout << "Invalid option. Please try again.\n";
        }
    } while (choice != 10);

    return 0;
}