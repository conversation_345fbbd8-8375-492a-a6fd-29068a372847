# Employee Attendance System - Compilation and Execution Guide

## Group Information
**Group Members:**
1. Eden Yehualashet [IHRCS-352438-16]
2. <PERSON> [IHRCS-9781-16]
3. TEMESGEN ABEBE [IHRCS-328669-16]
4. MIHRETA<PERSON> NIGATU [IHRCS-9599-16]
5. ERMIAS GIRMA [IHRCS-829949-16]

**Submission Date:** June 04, 2025

## ⚠️ CRITICAL: Correct Compilation Command

**WRONG (will cause linker errors):**
```bash
g++ main.cpp -o main
```

**CORRECT:**
```bash
g++ main.cpp employee_attendance.cpp -o employee_attendance
```

## Prerequisites

To compile and run this Employee Attendance System, you need a C++ compiler that supports C++11 or later.

### Windows Options:

1. **MinGW-w64** (Recommended for Windows)
   - Download from: https://www.mingw-w64.org/downloads/
   - Or install via MSYS2: https://www.msys2.org/

2. **Microsoft Visual Studio**
   - Download Visual Studio Community (free)
   - Install with C++ development tools

3. **Code::Blocks with MinGW**
   - Download from: http://www.codeblocks.org/

4. **Dev-C++**
   - Download from: https://www.bloodshed.net/devcpp.html

### Linux/macOS:
```bash
# Ubuntu/Debian
sudo apt-get install g++

# CentOS/RHEL
sudo yum install gcc-c++

# macOS (with Homebrew)
brew install gcc
```

## Compilation Instructions

### Method 1: Using g++ (MinGW on Windows)

```bash
# Navigate to the project directory
cd Employee_Attendance_System

# Compile the main program
g++ -std=c++11 -Wall -Wextra -O2 main.cpp employee_attendance.cpp -o employee_attendance.exe

# Compile the test program
g++ -std=c++11 -Wall -Wextra -O2 test_functionality.cpp employee_attendance.cpp -o test_functionality.exe
```

### Method 2: Using Microsoft Visual Studio Compiler (cl)

```cmd
# Open Developer Command Prompt for Visual Studio
# Navigate to project directory
cd Employee_Attendance_System

# Compile the main program
cl /EHsc /std:c++11 main.cpp employee_attendance.cpp /Fe:employee_attendance.exe

# Compile the test program
cl /EHsc /std:c++11 test_functionality.cpp employee_attendance.cpp /Fe:test_functionality.exe
```

### Method 3: Using Makefile (if make is available)

```bash
# Navigate to project directory
cd Employee_Attendance_System

# Compile
make

# Run
make run

# Clean
make clean
```

## Running the Programs

### Main Interactive Program
```bash
# Windows
./employee_attendance.exe

# Linux/macOS
./employee_attendance
```

### Test Program
```bash
# Windows
./test_functionality.exe

# Linux/macOS
./test_functionality
```

## IDE Setup Instructions

### Visual Studio Code
1. Install the C/C++ extension
2. Open the project folder
3. Configure tasks.json for building:

```json
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "build",
            "type": "shell",
            "command": "g++",
            "args": [
                "-std=c++11",
                "-Wall",
                "-Wextra",
                "-O2",
                "main.cpp",
                "employee_attendance.cpp",
                "-o",
                "employee_attendance.exe"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            }
        }
    ]
}
```

### Code::Blocks
1. Create new project (Console Application)
2. Add all .cpp and .h files to project
3. Build and run (F9)

### Visual Studio
1. Create new project (Console App)
2. Add existing files to project
3. Build solution (Ctrl+Shift+B)
4. Run (F5 or Ctrl+F5)

## Troubleshooting

### Common Issues:

1. **Compiler not found**
   - Ensure compiler is installed and in PATH
   - Try full path to compiler

2. **C++11 features not recognized**
   - Add `-std=c++11` flag for g++
   - Use `/std:c++11` for MSVC

3. **Linking errors**
   - Ensure all .cpp files are included in compilation
   - Check for missing semicolons or braces

4. **Runtime errors**
   - Check for infinite loops in circular list operations
   - Verify proper memory management

### Example Compilation Commands:

```bash
# Basic compilation
g++ main.cpp employee_attendance.cpp -o program

# With all flags
g++ -std=c++11 -Wall -Wextra -g -O2 main.cpp employee_attendance.cpp -o program

# Debug version
g++ -std=c++11 -Wall -Wextra -g -DDEBUG main.cpp employee_attendance.cpp -o program_debug
```

## Testing the Implementation

### Automated Testing
Run the test program to verify all functionality:
```bash
./test_functionality.exe
```

### Manual Testing
1. Run the main program
2. Choose option 17 (Run Demo Test) for comprehensive testing
3. Or manually test individual features using the menu

### Expected Output
The demo test should show:
- Employee addition and removal
- Check-in/check-out operations
- Search functionality
- Update operations
- Sorting algorithms
- Statistical reporting

## Performance Notes

- **Time Complexity**: Most operations are O(n) due to linear search
- **Space Complexity**: O(n) for storing n employees
- **Memory Usage**: Each node requires space for Employee data + 2 pointers
- **Sorting**: Uses bubble sort O(n²) - suitable for small datasets

## File Dependencies

```
main.cpp                    -> employee_attendance.h
employee_attendance.cpp     -> employee_attendance.h
test_functionality.cpp      -> employee_attendance.h
```

Ensure all files are in the same directory for compilation.

## Next Steps

After successful compilation and testing:
1. Experiment with different employee data
2. Test edge cases (empty list, single employee, etc.)
3. Modify the code to add new features
4. Optimize algorithms for better performance
5. Add persistent storage capabilities
