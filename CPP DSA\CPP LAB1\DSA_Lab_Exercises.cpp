/*
 * Data Structures and Algorithms Lab Exercises
 * Group Members: [Add your group member names here]
 * Date: [Add current date]
 * 
 * This program implements:
 * 1. Linear Search and Binary Search algorithms
 * 2. Sorting algorithms (Insertion, Selection, Bubble Sort)
 * 3. Singly Linked List operations
 * 4. Stack and Queue operations using arrays
 */

#include <iostream>
#include <algorithm>
using namespace std;

// Global constants
const int MAX_SIZE = 100;

// Exercise 1 & 2: Search and Sort Functions
void AcceptList(int List[], int Size) {
    cout << "Enter " << Size << " elements: ";
    for (int i = 0; i < Size; i++) {
        cin >> List[i];
    }
}

int LinearSearch(int List[], int Size, int Key) {
    for (int i = 0; i < Size; i++) {
        if (List[i] == Key) {
            return i; // Return index if found
        }
    }
    return -1; // Return -1 if not found
}

int BinarySearch(int List[], int Size, int Key) {
    int left = 0, right = Size - 1;
    while (left <= right) {
        int mid = left + (right - left) / 2;
        if (List[mid] == Key) {
            return mid;
        }
        if (List[mid] < Key) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }
    return -1;
}

void InsertSort(int List[], int Size) {
    for (int i = 1; i < Size; i++) {
        int key = List[i];
        int j = i - 1;
        while (j >= 0 && List[j] > key) {
            List[j + 1] = List[j];
            j--;
        }
        List[j + 1] = key;
    }
}

void SelectSort(int List[], int Size) {
    for (int i = 0; i < Size - 1; i++) {
        int minIndex = i;
        for (int j = i + 1; j < Size; j++) {
            if (List[j] < List[minIndex]) {
                minIndex = j;
            }
        }
        swap(List[i], List[minIndex]);
    }
}

void BubbleSort(int List[], int Size) {
    for (int i = 0; i < Size - 1; i++) {
        for (int j = 0; j < Size - i - 1; j++) {
            if (List[j] > List[j + 1]) {
                swap(List[j], List[j + 1]);
            }
        }
    }
}

void DisplayArray(int List[], int Size) {
    cout << "Array: ";
    for (int i = 0; i < Size; i++) {
        cout << List[i] << " ";
    }
    cout << endl;
}

int Menu() {
    cout << "\n=== DSA Lab Menu ===" << endl;
    cout << "1. Linear Search" << endl;
    cout << "2. Binary Search" << endl;
    cout << "3. Insertion Sort" << endl;
    cout << "4. Selection Sort" << endl;
    cout << "5. Bubble Sort" << endl;
    cout << "6. Linked List Operations" << endl;
    cout << "7. Stack Operations" << endl;
    cout << "8. Queue Operations" << endl;
    cout << "9. Exit" << endl;
    cout << "Enter your choice: ";
    int choice;
    cin >> choice;
    return choice;
}

// Exercise 3: Linked List Implementation
struct Node {
    int data;
    Node* next;
    
    Node(int value) : data(value), next(nullptr) {}
};

class LinkedList {
private:
    Node* head;
    
public:
    LinkedList() : head(nullptr) {}
    
    void addToStart(int data) {
        Node* newNode = new Node(data);
        newNode->next = head;
        head = newNode;
        cout << "Added " << data << " to start" << endl;
    }
    
    void addToEnd(int data) {
        Node* newNode = new Node(data);
        if (head == nullptr) {
            head = newNode;
        } else {
            Node* temp = head;
            while (temp->next != nullptr) {
                temp = temp->next;
            }
            temp->next = newNode;
        }
        cout << "Added " << data << " to end" << endl;
    }
    
    void removeFromStart() {
        if (head == nullptr) {
            cout << "List is empty" << endl;
            return;
        }
        Node* temp = head;
        head = head->next;
        cout << "Removed " << temp->data << " from start" << endl;
        delete temp;
    }
    
    void removeFromEnd() {
        if (head == nullptr) {
            cout << "List is empty" << endl;
            return;
        }
        if (head->next == nullptr) {
            cout << "Removed " << head->data << " from end" << endl;
            delete head;
            head = nullptr;
            return;
        }
        Node* temp = head;
        while (temp->next->next != nullptr) {
            temp = temp->next;
        }
        cout << "Removed " << temp->next->data << " from end" << endl;
        delete temp->next;
        temp->next = nullptr;
    }
    
    void traverse() {
        if (head == nullptr) {
            cout << "List is empty" << endl;
            return;
        }
        cout << "List: ";
        Node* temp = head;
        while (temp != nullptr) {
            cout << temp->data << " ";
            temp = temp->next;
        }
        cout << endl;
    }
    
    ~LinkedList() {
        while (head != nullptr) {
            Node* temp = head;
            head = head->next;
            delete temp;
        }
    }
};

// Exercise 4: Stack Implementation
class Stack {
private:
    int arr[MAX_SIZE];
    int top;
    
public:
    Stack() : top(-1) {}
    
    void push(int data) {
        if (top >= MAX_SIZE - 1) {
            cout << "Stack overflow" << endl;
            return;
        }
        arr[++top] = data;
        cout << "Pushed " << data << " to stack" << endl;
    }
    
    void pop() {
        if (top < 0) {
            cout << "Stack underflow" << endl;
            return;
        }
        cout << "Popped " << arr[top--] << " from stack" << endl;
    }
    
    void display() {
        if (top < 0) {
            cout << "Stack is empty" << endl;
            return;
        }
        cout << "Stack: ";
        for (int i = top; i >= 0; i--) {
            cout << arr[i] << " ";
        }
        cout << endl;
    }
};

// Exercise 4: Queue Implementation
class Queue {
private:
    int arr[MAX_SIZE];
    int front, rear;
    
public:
    Queue() : front(-1), rear(-1) {}
    
    void enqueue(int data) {
        if (rear >= MAX_SIZE - 1) {
            cout << "Queue overflow" << endl;
            return;
        }
        if (front == -1) front = 0;
        arr[++rear] = data;
        cout << "Enqueued " << data << " to queue" << endl;
    }
    
    void dequeue() {
        if (front == -1 || front > rear) {
            cout << "Queue underflow" << endl;
            return;
        }
        cout << "Dequeued " << arr[front++] << " from queue" << endl;
        if (front > rear) {
            front = rear = -1; // Reset queue when empty
        }
    }
    
    void display() {
        if (front == -1) {
            cout << "Queue is empty" << endl;
            return;
        }
        cout << "Queue: ";
        for (int i = front; i <= rear; i++) {
            cout << arr[i] << " ";
        }
        cout << endl;
    }
};

// Function prototypes for menu operations
void handleLinkedList();
void handleStack();
void handleQueue();

// Main function
int main() {
    cout << "=== Data Structures and Algorithms Lab Exercises ===" << endl;
    cout << "Group Members: [Add your names here]" << endl;
    cout << "Date: [Add current date]" << endl << endl;

    int choice;
    do {
        choice = Menu();

        switch (choice) {
            case 1:
            case 2:
            case 3:
            case 4:
            case 5: {
                int arr[MAX_SIZE];
                int size, key, result;

                cout << "Enter size of array (max " << MAX_SIZE << "): ";
                cin >> size;

                if (size <= 0 || size > MAX_SIZE) {
                    cout << "Invalid size!" << endl;
                    break;
                }

                AcceptList(arr, size);
                DisplayArray(arr, size);

                switch (choice) {
                    case 1: // Linear Search
                        cout << "Enter element to search: ";
                        cin >> key;
                        result = LinearSearch(arr, size, key);
                        if (result != -1) {
                            cout << "Element found at index " << result << endl;
                        } else {
                            cout << "Element not found" << endl;
                        }
                        break;

                    case 2: // Binary Search
                        cout << "Sorting array first for binary search..." << endl;
                        sort(arr, arr + size); // Sort the array first
                        DisplayArray(arr, size);
                        cout << "Enter element to search: ";
                        cin >> key;
                        result = BinarySearch(arr, size, key);
                        if (result != -1) {
                            cout << "Element found at index " << result << endl;
                        } else {
                            cout << "Element not found" << endl;
                        }
                        break;

                    case 3: // Insertion Sort
                        InsertSort(arr, size);
                        cout << "Array after Insertion Sort:" << endl;
                        DisplayArray(arr, size);
                        break;

                    case 4: // Selection Sort
                        SelectSort(arr, size);
                        cout << "Array after Selection Sort:" << endl;
                        DisplayArray(arr, size);
                        break;

                    case 5: // Bubble Sort
                        BubbleSort(arr, size);
                        cout << "Array after Bubble Sort:" << endl;
                        DisplayArray(arr, size);
                        break;
                }
                break;
            }
            case 6:
                handleLinkedList();
                break;
            case 7:
                handleStack();
                break;
            case 8:
                handleQueue();
                break;
            case 9:
                cout << "Exiting program. Thank you!" << endl;
                break;
            default:
                cout << "Invalid choice! Please try again." << endl;
        }
    } while (choice != 9);

    return 0;
}

// Implementation of menu handler functions

void handleLinkedList() {
    LinkedList list;
    int choice, data;

    do {
        cout << "\n=== Linked List Operations ===" << endl;
        cout << "1. Add to start" << endl;
        cout << "2. Add to end" << endl;
        cout << "3. Remove from start" << endl;
        cout << "4. Remove from end" << endl;
        cout << "5. Traverse list" << endl;
        cout << "6. Back to main menu" << endl;
        cout << "Enter choice: ";
        cin >> choice;

        switch (choice) {
            case 1:
                cout << "Enter data: ";
                cin >> data;
                list.addToStart(data);
                break;
            case 2:
                cout << "Enter data: ";
                cin >> data;
                list.addToEnd(data);
                break;
            case 3:
                list.removeFromStart();
                break;
            case 4:
                list.removeFromEnd();
                break;
            case 5:
                list.traverse();
                break;
            case 6:
                cout << "Returning to main menu..." << endl;
                break;
            default:
                cout << "Invalid choice!" << endl;
        }
    } while (choice != 6);
}

void handleStack() {
    Stack stack;
    int choice, data;

    do {
        cout << "\n=== Stack Operations ===" << endl;
        cout << "1. Push" << endl;
        cout << "2. Pop" << endl;
        cout << "3. Display stack" << endl;
        cout << "4. Back to main menu" << endl;
        cout << "Enter choice: ";
        cin >> choice;

        switch (choice) {
            case 1:
                cout << "Enter data to push: ";
                cin >> data;
                stack.push(data);
                break;
            case 2:
                stack.pop();
                break;
            case 3:
                stack.display();
                break;
            case 4:
                cout << "Returning to main menu..." << endl;
                break;
            default:
                cout << "Invalid choice!" << endl;
        }
    } while (choice != 4);
}

void handleQueue() {
    Queue queue;
    int choice, data;

    do {
        cout << "\n=== Queue Operations ===" << endl;
        cout << "1. Enqueue" << endl;
        cout << "2. Dequeue" << endl;
        cout << "3. Display queue" << endl;
        cout << "4. Back to main menu" << endl;
        cout << "Enter choice: ";
        cin >> choice;

        switch (choice) {
            case 1:
                cout << "Enter data to enqueue: ";
                cin >> data;
                queue.enqueue(data);
                break;
            case 2:
                queue.dequeue();
                break;
            case 3:
                queue.display();
                break;
            case 4:
                cout << "Returning to main menu..." << endl;
                break;
            default:
                cout << "Invalid choice!" << endl;
        }
    } while (choice != 4);
}
