MIT License

Copyright (c) 2025 Employee Attendance System Team
- Eden Yehualashet [IHRCS-352438-16]
- <PERSON> [IHRCS-9781-16]
- TEMESGEN ABEBE [IHRCS-328669-16]
- MIHRETAB NIGATU [IHRCS-9599-16]
- ERMIAS GIRMA [IHRCS-829949-16]

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

---

Educational Use Notice:
This project was developed as part of a Data Structures and Algorithms course
assignment. It demonstrates the implementation of a doubly-circular linked list
data structure in a practical employee attendance management system.

The code is provided for educational purposes and may be used by students and
educators for learning and teaching data structures and algorithms concepts.

For academic integrity, please ensure proper attribution when using this code
for educational purposes.
