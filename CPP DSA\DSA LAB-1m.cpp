/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> Nigatu <EMAIL>
 * @Date: 2025-05-28 12:45:55
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2025-05-28 12:53:24
 * @FilePath: \CPP DSA\ede.cpp
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include <iostream>
using namespace std;

// Accept sorted numbers into an array
void acceptNumbers(int arr[], int n) {
    cout << "Enter " << n << " sorted numbers (ascending order):\n";
    for (int i = 0; i < n; ++i) {
        cin >> arr[i];
    }
}

// Linear search
int linearSearch(int arr[], int n, int key) {
    for (int i = 0; i < n; ++i) {
        if (arr[i] == key)
            return i;
    }
    return -1;
}

// Binary search
int binarySearch(int arr[], int n, int key) {
    int low = 0, high = n - 1;
    while (low <= high) {
        int mid = (low + high) / 2;
        if (arr[mid] == key)
            return mid;
        else if (arr[mid] < key)
            low = mid + 1;
        else
            high = mid - 1;
    }
    return -1;
}

int main() {
    int numbers[100];  // fixed-size array with max 100 elements
    int n, key;

    cout << "How many numbers do you want to enter? ";
    cin >> n;

    if (n > 100) {
        cout << "Too many numbers. Max is 100.\n";
        return 1;
    }

    acceptNumbers(numbers, n);

    cout << "Enter the number to search: ";
    cin >> key;

    int linearIndex = linearSearch(numbers, n, key);
    if (linearIndex != -1)
        cout << "Linear Search: Element found at index " << linearIndex << endl;
    else
        cout << "Linear Search: Element not found.\n";

    int binaryIndex = binarySearch(numbers, n, key);
    if (binaryIndex != -1)
        cout << "Binary Search: Element found at index " << binaryIndex << endl;
    else
        cout << "Binary Search: Element not found.\n";

    return 0;
}