#include "employee_attendance.h"
#include <iostream>
#include <cassert>

using namespace std;

// Simple test function to verify basic functionality
void testBasicOperations() {
    cout << "=== Testing Basic Operations ===" << endl;
    
    EmployeeAttendanceSystem system;
    
    // Test 1: Add employees
    cout << "\nTest 1: Adding employees..." << endl;
    system.addEmployee(101, "<PERSON>", "IT");
    system.addEmployee(102, "<PERSON>", "HR");
    system.addEmployee(103, "<PERSON>", "IT");
    
    // Test 2: Display all employees
    cout << "\nTest 2: Displaying all employees..." << endl;
    system.displayAll();
    
    // Test 3: Search operations
    cout << "\nTest 3: Search operations..." << endl;
    Node* found = system.searchById(102);
    if (found) {
        cout << "Found employee with ID 102: ";
        found->data.displayInfo();
    }
    
    found = system.searchByName("<PERSON>");
    if (found) {
        cout << "Found employee with name '<PERSON>': ";
        found->data.displayInfo();
    }
    
    // Test 4: Check-in operations
    cout << "\nTest 4: Check-in operations..." << endl;
    system.checkIn(101);
    system.checkIn(103);
    
    // Test 5: Display checked-in employees
    cout << "\nTest 5: Displaying checked-in employees..." << endl;
    system.displayCheckedInEmployees();
    
    // Test 6: Check-out operations
    cout << "\nTest 6: Check-out operations..." << endl;
    system.checkOut(101);
    
    // Test 7: Display checked-out employees
    cout << "\nTest 7: Displaying checked-out employees..." << endl;
    system.displayCheckedOutEmployees();
    
    // Test 8: Update operations
    cout << "\nTest 8: Update operations..." << endl;
    system.updateEmployeeName(102, "Robert Smith");
    system.updateEmployeeDepartment(103, "Development");
    
    // Test 9: Display by department
    cout << "\nTest 9: Display by department..." << endl;
    system.displayEmployeesByDepartment("IT");
    system.displayEmployeesByDepartment("Development");
    
    // Test 10: Sorting operations
    cout << "\nTest 10: Sorting operations..." << endl;
    system.sortByName();
    system.displayAll();
    
    system.sortById();
    system.displayAll();
    
    // Test 11: Statistics
    cout << "\nTest 11: Statistics..." << endl;
    system.displayStatistics();
    
    // Test 12: Remove employee
    cout << "\nTest 12: Remove employee..." << endl;
    system.removeEmployee(102);
    system.displayAll();
    
    cout << "\n=== All Tests Completed Successfully! ===" << endl;
}

void testEdgeCases() {
    cout << "\n=== Testing Edge Cases ===" << endl;
    
    EmployeeAttendanceSystem system;
    
    // Test empty system
    cout << "\nTesting empty system operations..." << endl;
    system.displayAll();
    system.displayCheckedInEmployees();
    system.displayStatistics();
    
    // Test duplicate employee ID
    cout << "\nTesting duplicate employee ID..." << endl;
    system.addEmployee(100, "John Doe", "IT");
    system.addEmployee(100, "Jane Doe", "HR"); // Should fail
    
    // Test operations on non-existent employee
    cout << "\nTesting operations on non-existent employee..." << endl;
    system.checkIn(999);  // Should fail
    system.checkOut(999); // Should fail
    system.removeEmployee(999); // Should fail
    
    // Test double check-in/check-out
    cout << "\nTesting double check-in/check-out..." << endl;
    system.checkIn(100);
    system.checkIn(100);   // Should fail - already checked in
    system.checkOut(100);
    system.checkOut(100);  // Should fail - already checked out
    
    cout << "\n=== Edge Case Tests Completed! ===" << endl;
}

void demonstrateDoublyCircularList() {
    cout << "\n=== Demonstrating Doubly-Circular Linked List Properties ===" << endl;
    
    EmployeeAttendanceSystem system;
    
    // Add employees to demonstrate circular nature
    system.addEmployee(1, "First Employee", "Dept1");
    system.addEmployee(2, "Second Employee", "Dept2");
    system.addEmployee(3, "Third Employee", "Dept3");
    system.addEmployee(4, "Fourth Employee", "Dept4");
    
    cout << "\nAdded 4 employees to demonstrate circular list properties:" << endl;
    system.displayAll();
    
    cout << "\nThe doubly-circular linked list has the following properties:" << endl;
    cout << "1. Each node has both 'next' and 'prev' pointers" << endl;
    cout << "2. The last node's 'next' points to the first node" << endl;
    cout << "3. The first node's 'prev' points to the last node" << endl;
    cout << "4. You can traverse in both directions infinitely" << endl;
    cout << "5. No NULL pointers exist in a non-empty list" << endl;
    
    // Demonstrate sorting (which uses the circular nature)
    cout << "\nDemonstrating sorting (uses circular traversal):" << endl;
    system.sortByName();
    system.displayAll();
    
    cout << "\n=== Doubly-Circular List Demonstration Complete! ===" << endl;
}

int main() {
    cout << "========================================" << endl;
    cout << "  EMPLOYEE ATTENDANCE SYSTEM TESTING" << endl;
    cout << "========================================" << endl;
    
    try {
        // Run comprehensive tests
        testBasicOperations();
        testEdgeCases();
        demonstrateDoublyCircularList();
        
        cout << "\n========================================" << endl;
        cout << "  ALL TESTS PASSED SUCCESSFULLY!" << endl;
        cout << "========================================" << endl;
        
    } catch (const exception& e) {
        cout << "Error during testing: " << e.what() << endl;
        return 1;
    }
    
    return 0;
}
