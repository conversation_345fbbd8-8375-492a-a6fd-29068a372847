@echo off
REM Employee Attendance System - GitHub Deployment Script
REM Group Members: Eden, Abel, Temesgen, Mihretab, Ermias

echo ========================================
echo   Employee Attendance System
echo   GitHub Deployment Helper
echo ========================================
echo.

echo This script will help you deploy your project to GitHub.
echo.

echo Step 1: Checking if Git is installed...
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Git is not installed or not in PATH.
    echo Please install Git from: https://git-scm.com/download/win
    pause
    exit /b 1
)
echo ✓ Git is installed.
echo.

echo Step 2: Checking project files...
if not exist "employee_attendance.h" (
    echo ERROR: employee_attendance.h not found!
    echo Make sure you're running this from the project directory.
    pause
    exit /b 1
)
if not exist "employee_attendance.cpp" (
    echo ERROR: employee_attendance.cpp not found!
    pause
    exit /b 1
)
if not exist "main.cpp" (
    echo ERROR: main.cpp not found!
    pause
    exit /b 1
)
echo ✓ All source files found.
echo.

echo Step 3: Testing compilation...
echo Compiling project to verify everything works...
g++ main.cpp employee_attendance.cpp -o test_compile.exe
if %errorlevel% neq 0 (
    echo ERROR: Compilation failed!
    echo Please fix compilation errors before deploying.
    pause
    exit /b 1
)
echo ✓ Compilation successful.
del test_compile.exe >nul 2>&1
echo.

echo Step 4: Repository setup instructions
echo.
echo BEFORE RUNNING THIS SCRIPT, YOU NEED TO:
echo 1. Create a GitHub account at https://github.com
echo 2. Create a new repository named: employee-attendance-system
echo 3. Copy the repository URL (e.g., https://github.com/username/employee-attendance-system.git)
echo.

set /p repo_url="Enter your GitHub repository URL: "
if "%repo_url%"=="" (
    echo ERROR: Repository URL is required.
    pause
    exit /b 1
)

echo.
echo Step 5: Initializing Git repository...
if exist ".git" (
    echo Git repository already exists.
) else (
    git init
    echo ✓ Git repository initialized.
)

echo.
echo Step 6: Setting up remote origin...
git remote remove origin >nul 2>&1
git remote add origin %repo_url%
echo ✓ Remote origin set to: %repo_url%

echo.
echo Step 7: Preparing files for commit...

REM Copy the GitHub README if it exists
if exist "README_GITHUB.md" (
    copy "README_GITHUB.md" "README.md" >nul
    echo ✓ GitHub README copied.
)

echo.
echo Step 8: Adding files to Git...
git add .
echo ✓ Files added to staging area.

echo.
echo Step 9: Creating initial commit...
git commit -m "Initial commit: Employee Attendance System with comprehensive documentation"
if %errorlevel% neq 0 (
    echo ERROR: Commit failed. This might be because:
    echo - Git user name/email not configured
    echo - No changes to commit
    echo.
    echo To configure Git user:
    echo git config --global user.name "Your Name"
    echo git config --global user.email "<EMAIL>"
    pause
    exit /b 1
)
echo ✓ Initial commit created.

echo.
echo Step 10: Pushing to GitHub...
echo This may prompt for your GitHub username and password/token.
git push -u origin main
if %errorlevel% neq 0 (
    echo.
    echo Push failed. This might be because:
    echo - Authentication failed (check username/password)
    echo - Repository doesn't exist on GitHub
    echo - Network connection issues
    echo.
    echo You can try pushing manually with:
    echo git push -u origin main
    pause
    exit /b 1
)

echo.
echo ========================================
echo   🎉 DEPLOYMENT SUCCESSFUL! 🎉
echo ========================================
echo.
echo Your Employee Attendance System is now on GitHub!
echo Repository URL: %repo_url%
echo.
echo Next steps:
echo 1. Visit your repository on GitHub
echo 2. Add repository topics: cpp, data-structures, linked-list
echo 3. Create a release for your submission
echo 4. Share the URL with your instructor
echo.
echo For presentation, you can now show:
echo - Professional GitHub repository
echo - Comprehensive documentation
echo - Working code with proper version control
echo.
pause
