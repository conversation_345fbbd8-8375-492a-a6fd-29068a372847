# DSA Lab Exercises - Simplified Implementation

## Overview
This program implements the essential Data Structures and Algorithms lab exercises in a single C++ file as requested for quick lab class exercises.

## Features Implemented

### Core Algorithms (5 Essential Functions)
- **Linear Search**: Searches for an element in an unsorted array
- **Binary Search**: Searches for an element in a sorted array (automatically sorts first)
- **Insertion Sort**: Sorts array using insertion sort algorithm
- **Selection Sort**: Sorts array using selection sort algorithm
- **Bubble Sort**: Sorts array using bubble sort algorithm

## Files Available
- **DSA_Lab_Simple.cpp**: Simplified version with only the 5 core algorithms
- **DSA_Lab_Exercises.cpp**: Complete version with linked lists, stacks, and queues

## How to Compile and Run

### For Simplified Version (Recommended)
```bash
g++ DSA_Lab_Simple.cpp -o DSA_Lab_Simple.exe
.\DSA_Lab_Simple.exe
```

### For Complete Version
```bash
g++ DSA_Lab_Exercises.cpp -o DSA_Lab_Exercises.exe
.\DSA_Lab_Exercises.exe
```

## Program Structure

### Functions Implemented
- `AcceptList()`: Accepts array elements from user
- `LinearSearch()`: Implements linear search algorithm
- `BinarySearch()`: Implements binary search algorithm
- `InsertSort()`: Implements insertion sort algorithm
- `SelectSort()`: Implements selection sort algorithm
- `BubbleSort()`: Implements bubble sort algorithm
- `Menu()`: Displays main menu and returns user choice

### Classes Implemented
- `LinkedList`: Singly linked list with basic operations
- `Stack`: Array-based stack implementation
- `Queue`: Array-based queue implementation

## Usage Instructions

1. **Run the program**: Execute the compiled file
2. **Choose operation**: Select from the main menu (1-9)
3. **Follow prompts**: Enter data as requested by the program
4. **View results**: See the output of your chosen operation
5. **Repeat or Exit**: Continue with other operations or exit (option 9)

## Sample Menu (Simplified Version)
```
=== DSA Lab Menu ===
1. Linear Search
2. Binary Search
3. Insertion Sort
4. Selection Sort
5. Bubble Sort
6. Exit
```

## Notes
- Maximum array size is set to 100 elements
- All algorithms use basic implementations suitable for educational purposes
- The program includes extensive comments for clarity
- Error handling is implemented for invalid inputs
- Memory management is handled properly for dynamic data structures

## Group Information
Remember to add your group member names and current date in the source code header before submission.
