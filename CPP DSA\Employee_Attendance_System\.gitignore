# Employee Attendance System - Git Ignore File
# Group Members: <PERSON>, Abel, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>

# Compiled Object files
*.o
*.obj

# Precompiled Headers
*.gch
*.pch

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Fortran module files
*.mod
*.smod

# Compiled Static libraries
*.lai
*.la
*.a
*.lib

# Executables
*.exe
*.out
*.app

# Debug files
*.dSYM/
*.su
*.idb
*.pdb

# Kernel Module Compile Results
*.mod*
*.cmd
.tmp_versions/
modules.order
Module.symvers
Mkfile.old
dkms.conf

# IDE specific files
# Visual Studio
.vs/
*.vcxproj
*.vcxproj.filters
*.vcxproj.user
*.sln
*.suo
*.user
*.userosscache
*.sln.docstates

# Visual Studio Code
.vscode/
*.code-workspace

# Code::Blocks
*.cbp
*.layout
*.depend

# Dev-C++
*.dev

# CLion
.idea/
cmake-build-*/

# Qt Creator
*.pro.user
*.pro.user.*
*.qbs.user
*.qbs.user.*
*.moc
*.moc.cpp
*.qm
*.prl

# Build directories
build/
Build/
BUILD/
debug/
Debug/
release/
Release/
bin/
obj/

# CMake
CMakeCache.txt
CMakeFiles/
CMakeScripts/
cmake_install.cmake
install_manifest.txt
compile_commands.json
CTestTestfile.cmake

# Make
Makefile
*.make

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
*.lnk

# Linux
*~

# Log files
*.log

# Backup files
*.bak
*.backup

# Test files (keep source, ignore compiled)
test_*.exe
test_*.out

# Documentation build files
docs/_build/
docs/html/
docs/latex/

# Package files
*.zip
*.tar.gz
*.rar

# Keep these files (override above rules)
!README.md
!*.md
!*.h
!*.cpp
!Makefile
!LICENSE

# Project specific
# Keep the main executable for demonstration
!employee_attendance.exe
!main.exe
