/*
================================================================================
                    EMPLOYEE ATTENDANCE SYSTEM IMPLEMENTATION
================================================================================

GROUP MEMBERS:
1. <PERSON> Yehualashet [IHRCS-352438-16]
2. <PERSON> [IHRCS-9781-16]
3. TEMESGEN ABEBE [IHRCS-328669-16]
4. MIHRETAB NIGATU [IHRCS-9599-16]
5. ERMIAS GIRMA [IHRCS-829949-16]

SUBMISSION DATE: June 04, 2025

This file contains the implementation of all methods declared in the
EmployeeAttendanceSystem class. Each method is thoroughly documented
with algorithm explanations and complexity analysis.

================================================================================
*/

#include "employee_attendance.h"
#include <algorithm>

/*
================================================================================
                                BASIC OPERATIONS
================================================================================
*/

/**
 * @brief Add employee to the doubly-circular linked list
 *
 * ALGORITHM:
 * 1. Check if employee with same ID already exists (prevent duplicates)
 * 2. Create new Employee object and Node
 * 3. If list is empty: make new node point to itself (circular)
 * 4. If list has nodes: insert at end (before head) maintaining circular property
 * 5. Update size counter
 *
 * CIRCULAR LIST MAINTENANCE:
 * - New node's next points to head
 * - New node's prev points to current tail
 * - Current tail's next points to new node
 * - Head's prev points to new node (new tail)
 *
 * Time Complexity: O(n) - due to duplicate check
 * Space Complexity: O(1) - only allocates one new node
 */
void EmployeeAttendanceSystem::addEmployee(int id, const string &name, const string &department)
{
    // Step 1: Check if employee already exists to prevent duplicates
    if (searchById(id) != nullptr)
    {
        cout << "Employee with ID " << id << " already exists!" << endl;
        return;
    }

    // Step 2: Create new employee and node
    Employee newEmp(id, name, department);
    Node *newNode = new Node(newEmp);

    if (head == nullptr)
    {
        // Step 3: First node - create circular reference to itself
        head = newNode;
        newNode->next = newNode; // Points to itself
        newNode->prev = newNode; // Points to itself
    }
    else
    {
        // Step 4: Insert at the end (before head) to maintain circular property
        Node *tail = head->prev; // Get current tail (head's previous)

        // Update new node's pointers
        newNode->next = head; // New node points to head
        newNode->prev = tail; // New node points to current tail

        // Update existing nodes' pointers
        tail->next = newNode; // Old tail points to new node
        head->prev = newNode; // Head's prev points to new node (new tail)
    }

    // Step 5: Update size and confirm addition
    size++;
    cout << "Employee " << name << " (ID: " << id << ") added successfully!" << endl;
}

/**
 * @brief Remove employee from the doubly-circular linked list
 *
 * ALGORITHM:
 * 1. Search for the employee by ID
 * 2. If not found, return false
 * 3. If only one node: set head to nullptr
 * 4. If multiple nodes: update prev/next pointers to bypass the node
 * 5. If removing head: update head pointer to next node
 * 6. Delete the node and update size
 *
 * CIRCULAR LIST MAINTENANCE:
 * - Previous node's next points to next node
 * - Next node's prev points to previous node
 * - Circular property maintained automatically
 *
 * Time Complexity: O(n) - due to search operation
 * Space Complexity: O(1) - only deallocates one node
 */
bool EmployeeAttendanceSystem::removeEmployee(int id)
{
    // Step 1: Search for the employee to remove
    Node *nodeToDelete = searchById(id);
    if (nodeToDelete == nullptr)
    {
        cout << "Employee with ID " << id << " not found!" << endl;
        return false;
    }

    if (size == 1)
    {
        // Step 3: Only one node - list becomes empty
        head = nullptr;
    }
    else
    {
        // Step 4: Multiple nodes - update links to bypass the node
        nodeToDelete->prev->next = nodeToDelete->next; // Previous node skips current
        nodeToDelete->next->prev = nodeToDelete->prev; // Next node skips current

        // Step 5: Update head if we're removing the head node
        if (nodeToDelete == head)
        {
            head = nodeToDelete->next; // Move head to next node
        }
    }

    // Step 6: Clean up and update counters
    cout << "Employee " << nodeToDelete->data.name << " (ID: " << id << ") removed successfully!" << endl;
    delete nodeToDelete; // Free memory
    size--;              // Decrease size counter
    return true;
}

/**
 * @brief Clear all employees from the system
 *
 * ALGORITHM:
 * 1. Check if list is empty
 * 2. Start from head and traverse the circular list
 * 3. Delete each node while moving to next
 * 4. Stop when we complete the circle (back to original head)
 * 5. Reset head and size
 *
 * CIRCULAR LIST TRAVERSAL:
 * - Use do-while loop to handle circular nature
 * - Store next pointer before deleting current node
 * - Continue until we reach the starting point again
 *
 * Time Complexity: O(n) - visits each node once
 * Space Complexity: O(1) - only uses temporary pointers
 */
void EmployeeAttendanceSystem::clear()
{
    // Step 1: Check if list is already empty
    if (head == nullptr)
        return;

    // Step 2-4: Traverse and delete all nodes in circular list
    Node *current = head;
    do
    {
        Node *next = current->next; // Store next before deletion
        delete current;             // Free current node's memory
        current = next;             // Move to next node
    } while (current != head); // Continue until full circle

    // Step 5: Reset list to empty state
    head = nullptr;
    size = 0;
}

/*
================================================================================
                            ATTENDANCE OPERATIONS
================================================================================
*/

/**
 * @brief Check in an employee by ID
 *
 * ALGORITHM:
 * 1. Search for employee by ID
 * 2. Validate employee exists and is not already checked in
 * 3. Set check-in status to true
 * 4. Record current date and time as check-in timestamp
 * 5. Display confirmation message
 *
 * VALIDATION CHECKS:
 * - Employee must exist in system
 * - Employee must not already be checked in
 *
 * Time Complexity: O(n) - due to search operation
 * Space Complexity: O(1) - only updates existing data
 */
bool EmployeeAttendanceSystem::checkIn(int empId)
{
    // Step 1: Search for the employee
    Node *empNode = searchById(empId);
    if (empNode == nullptr)
    {
        cout << "Employee with ID " << empId << " not found!" << endl;
        return false;
    }

    // Step 2: Check if already checked in (prevent double check-in)
    if (empNode->data.isCheckedIn)
    {
        cout << "Employee " << empNode->data.name << " is already checked in!" << endl;
        return false;
    }

    // Step 3-4: Record check-in with current timestamp
    empNode->data.isCheckedIn = true;
    empNode->data.checkInTime = getCurrentTime();
    empNode->data.checkInDate = getCurrentDate();

    // Step 5: Confirm successful check-in
    cout << "Employee " << empNode->data.name << " checked in at "
         << empNode->data.checkInTime.toString() << " on "
         << empNode->data.checkInDate.toString() << endl;

    return true;
}

// Check-out operation
bool EmployeeAttendanceSystem::checkOut(int empId)
{
    Node *empNode = searchById(empId);
    if (empNode == nullptr)
    {
        cout << "Employee with ID " << empId << " not found!" << endl;
        return false;
    }

    if (!empNode->data.isCheckedIn)
    {
        cout << "Employee " << empNode->data.name << " is not checked in!" << endl;
        return false;
    }

    empNode->data.isCheckedIn = false;
    empNode->data.checkOutTime = getCurrentTime();
    empNode->data.checkOutDate = getCurrentDate();

    // Calculate hours worked
    empNode->data.hoursWorked = calculateHours(empNode->data.checkInTime, empNode->data.checkOutTime);

    cout << "Employee " << empNode->data.name << " checked out at "
         << empNode->data.checkOutTime.toString() << " on "
         << empNode->data.checkOutDate.toString()
         << ". Hours worked: " << fixed << setprecision(2) << empNode->data.hoursWorked << endl;

    return true;
}

// Search employee by ID
Node *EmployeeAttendanceSystem::searchById(int id)
{
    if (head == nullptr)
        return nullptr;

    Node *current = head;
    do
    {
        if (current->data.empId == id)
        {
            return current;
        }
        current = current->next;
    } while (current != head);

    return nullptr;
}

// Search employee by name
Node *EmployeeAttendanceSystem::searchByName(const string &name)
{
    if (head == nullptr)
        return nullptr;

    Node *current = head;
    do
    {
        if (current->data.name == name)
        {
            return current;
        }
        current = current->next;
    } while (current != head);

    return nullptr;
}

// Display employees by department
void EmployeeAttendanceSystem::displayEmployeesByDepartment(const string &department)
{
    if (head == nullptr)
    {
        cout << "No employees found!" << endl;
        return;
    }

    cout << "\n=== Employees in " << department << " Department ===" << endl;
    bool found = false;
    Node *current = head;
    do
    {
        if (current->data.department == department)
        {
            current->data.displayInfo();
            found = true;
        }
        current = current->next;
    } while (current != head);

    if (!found)
    {
        cout << "No employees found in " << department << " department." << endl;
    }
}

// Update employee name
bool EmployeeAttendanceSystem::updateEmployeeName(int id, const string &newName)
{
    Node *empNode = searchById(id);
    if (empNode == nullptr)
    {
        cout << "Employee with ID " << id << " not found!" << endl;
        return false;
    }

    string oldName = empNode->data.name;
    empNode->data.name = newName;
    cout << "Employee name updated from '" << oldName << "' to '" << newName << "'" << endl;
    return true;
}

// Update employee department
bool EmployeeAttendanceSystem::updateEmployeeDepartment(int id, const string &newDepartment)
{
    Node *empNode = searchById(id);
    if (empNode == nullptr)
    {
        cout << "Employee with ID " << id << " not found!" << endl;
        return false;
    }

    string oldDept = empNode->data.department;
    empNode->data.department = newDepartment;
    cout << "Employee department updated from '" << oldDept << "' to '" << newDepartment << "'" << endl;
    return true;
}

// Display all employees
void EmployeeAttendanceSystem::displayAll()
{
    if (head == nullptr)
    {
        cout << "No employees in the system!" << endl;
        return;
    }

    cout << "\n=== All Employees ===" << endl;
    Node *current = head;
    do
    {
        current->data.displayInfo();
        current = current->next;
    } while (current != head);
    cout << "Total employees: " << size << endl;
}

// Display checked-in employees
void EmployeeAttendanceSystem::displayCheckedInEmployees()
{
    if (head == nullptr)
    {
        cout << "No employees in the system!" << endl;
        return;
    }

    cout << "\n=== Checked-In Employees ===" << endl;
    bool found = false;
    Node *current = head;
    do
    {
        if (current->data.isCheckedIn)
        {
            current->data.displayInfo();
            found = true;
        }
        current = current->next;
    } while (current != head);

    if (!found)
    {
        cout << "No employees are currently checked in." << endl;
    }
}

// Display checked-out employees
void EmployeeAttendanceSystem::displayCheckedOutEmployees()
{
    if (head == nullptr)
    {
        cout << "No employees in the system!" << endl;
        return;
    }

    cout << "\n=== Checked-Out Employees ===" << endl;
    bool found = false;
    Node *current = head;
    do
    {
        if (!current->data.isCheckedIn)
        {
            current->data.displayInfo();
            found = true;
        }
        current = current->next;
    } while (current != head);

    if (!found)
    {
        cout << "No employees are currently checked out." << endl;
    }
}

// Sort by ID (bubble sort)
void EmployeeAttendanceSystem::sortById()
{
    if (head == nullptr || size <= 1)
        return;

    bool swapped;
    do
    {
        swapped = false;
        Node *current = head;
        do
        {
            if (current->data.empId > current->next->data.empId)
            {
                // Swap employee data
                Employee temp = current->data;
                current->data = current->next->data;
                current->next->data = temp;
                swapped = true;
            }
            current = current->next;
        } while (current->next != head);
    } while (swapped);

    cout << "Employees sorted by ID." << endl;
}

// Sort by name (bubble sort)
void EmployeeAttendanceSystem::sortByName()
{
    if (head == nullptr || size <= 1)
        return;

    bool swapped;
    do
    {
        swapped = false;
        Node *current = head;
        do
        {
            if (current->data.name > current->next->data.name)
            {
                // Swap employee data
                Employee temp = current->data;
                current->data = current->next->data;
                current->next->data = temp;
                swapped = true;
            }
            current = current->next;
        } while (current->next != head);
    } while (swapped);

    cout << "Employees sorted by name." << endl;
}

// Sort by hours worked (bubble sort)
void EmployeeAttendanceSystem::sortByHoursWorked()
{
    if (head == nullptr || size <= 1)
        return;

    bool swapped;
    do
    {
        swapped = false;
        Node *current = head;
        do
        {
            if (current->data.hoursWorked < current->next->data.hoursWorked)
            {
                // Swap employee data (descending order)
                Employee temp = current->data;
                current->data = current->next->data;
                current->next->data = temp;
                swapped = true;
            }
            current = current->next;
        } while (current->next != head);
    } while (swapped);

    cout << "Employees sorted by hours worked (descending)." << endl;
}

// Display statistics
void EmployeeAttendanceSystem::displayStatistics()
{
    if (head == nullptr)
    {
        cout << "No employees in the system!" << endl;
        return;
    }

    int checkedIn = 0, checkedOut = 0;
    double totalHours = 0.0;

    Node *current = head;
    do
    {
        if (current->data.isCheckedIn)
        {
            checkedIn++;
        }
        else
        {
            checkedOut++;
            totalHours += current->data.hoursWorked;
        }
        current = current->next;
    } while (current != head);

    cout << "\n=== Attendance Statistics ===" << endl;
    cout << "Total employees: " << size << endl;
    cout << "Currently checked in: " << checkedIn << endl;
    cout << "Currently checked out: " << checkedOut << endl;
    cout << "Total hours worked today: " << fixed << setprecision(2) << totalHours << endl;
    if (checkedOut > 0)
    {
        cout << "Average hours per employee: " << fixed << setprecision(2) << (totalHours / checkedOut) << endl;
    }
}
