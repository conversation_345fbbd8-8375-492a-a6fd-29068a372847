#include "employee_attendance.h"
#include <iostream>
#include <string>

EmployeeAttendanceSystem::EmployeeAttendanceSystem() : head(nullptr), size(0) {}

EmployeeAttendanceSystem::~EmployeeAttendanceSystem() {
    clear();
}

void EmployeeAttendanceSystem::addEmployee(const Employee& employee) {
    Node* newNode = new Node(employee);
    if (!head) {
        head = newNode;
        head->next = head;
        head->prev = head;
    } else {
        Node* tail = head->prev;
        tail->next = newNode;
        newNode->prev = tail;
        newNode->next = head;
        head->prev = newNode;
    }
    size++;
}

void EmployeeAttendanceSystem::removeEmployee(int id) {
    if (!head) return;

    Node* current = head;
    do {
        if (current->data.id == id) {
            if (current == head && current->next == head) {
                delete current;
                head = nullptr;
            } else {
                current->prev->next = current->next;
                current->next->prev = current->prev;
                if (current == head) {
                    head = current->next;
                }
                delete current;
            }
            size--;
            return;
        }
        current = current->next;
    } while (current != head);
}

void EmployeeAttendanceSystem::updateEmployee(int id, const std::string& name, const std::string& department) {
    Node* current = head;
    do {
        if (current->data.id == id) {
            current->data.name = name;
            current->data.department = department;
            return;
        }
        current = current->next;
    } while (current != head);
}

void EmployeeAttendanceSystem::checkIn(int id) {
    Node* current = head;
    do {
        if (current->data.id == id) {
            current->data.checkInTime = getCurrentTime();
            current->data.status = "Checked In";
            return;
        }
        current = current->next;
    } while (current != head);
}

void EmployeeAttendanceSystem::checkOut(int id) {
    Node* current = head;
    do {
        if (current->data.id == id) {
            current->data.checkOutTime = getCurrentTime();
            current->data.status = "Checked Out";
            return;
        }
        current = current->next;
    } while (current != head);
}

void EmployeeAttendanceSystem::clear() {
    while (head) {
        removeEmployee(head->data.id);
    }
}

std::string EmployeeAttendanceSystem::getCurrentTime() {
    // Implement a method to get the current time as a string
    return "Current Time"; // Placeholder
}