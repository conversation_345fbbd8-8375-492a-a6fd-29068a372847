// Complete Employee Attendance System - Single File Version
// Copy this entire code to an online C++ compiler

#include <iostream>
#include <string>
#include <ctime>
#include <iomanip>
#include <sstream>
using namespace std;

struct Time {
    int hour, minute, second;
    Time() : hour(0), minute(0), second(0) {}
    Time(int h, int m, int s) : hour(h), minute(m), second(s) {}
    string toString() const {
        stringstream ss;
        ss << setfill('0') << setw(2) << hour << ":" << setw(2) << minute << ":" << setw(2) << second;
        return ss.str();
    }
};

struct Date {
    int day, month, year;
    Date() : day(1), month(1), year(2024) {}
    Date(int d, int m, int y) : day(d), month(m), year(y) {}
    string toString() const {
        stringstream ss;
        ss << setfill('0') << setw(2) << day << "/" << setw(2) << month << "/" << year;
        return ss.str();
    }
};

struct Employee {
    int empId;
    string name, department;
    Date checkInDate, checkOutDate;
    Time checkInTime, checkOutTime;
    bool isCheckedIn;
    double hoursWorked;
    
    Employee() : empId(0), isCheckedIn(false), hoursWorked(0.0) {}
    Employee(int id, string n, string dept) : empId(id), name(n), department(dept), isCheckedIn(false), hoursWorked(0.0) {}
    
    void displayInfo() const {
        cout << "ID: " << empId << ", Name: " << name << ", Dept: " << department;
        if (isCheckedIn) {
            cout << " [CHECKED IN at " << checkInTime.toString() << "]";
        } else {
            cout << " [CHECKED OUT]";
            if (hoursWorked > 0) cout << " - Hours: " << fixed << setprecision(2) << hoursWorked;
        }
        cout << endl;
    }
};

struct Node {
    Employee data;
    Node* next;
    Node* prev;
    Node(const Employee& emp) : data(emp), next(nullptr), prev(nullptr) {}
};

class EmployeeAttendanceSystem {
private:
    Node* head;
    int size;
    
    Time getCurrentTime() {
        time_t now = time(0);
        tm* ltm = localtime(&now);
        return Time(ltm->tm_hour, ltm->tm_min, ltm->tm_sec);
    }
    
    Date getCurrentDate() {
        time_t now = time(0);
        tm* ltm = localtime(&now);
        return Date(ltm->tm_mday, 1 + ltm->tm_mon, 1900 + ltm->tm_year);
    }
    
    double calculateHours(const Time& checkIn, const Time& checkOut) {
        int totalMinutesIn = checkIn.hour * 60 + checkIn.minute;
        int totalMinutesOut = checkOut.hour * 60 + checkOut.minute;
        if (totalMinutesOut < totalMinutesIn) totalMinutesOut += 24 * 60;
        return (totalMinutesOut - totalMinutesIn) / 60.0;
    }

public:
    EmployeeAttendanceSystem() : head(nullptr), size(0) {}
    
    ~EmployeeAttendanceSystem() { clear(); }
    
    void addEmployee(int id, const string& name, const string& department) {
        if (searchById(id) != nullptr) {
            cout << "Employee with ID " << id << " already exists!" << endl;
            return;
        }
        
        Employee newEmp(id, name, department);
        Node* newNode = new Node(newEmp);
        
        if (head == nullptr) {
            head = newNode;
            newNode->next = newNode->prev = newNode;
        } else {
            Node* tail = head->prev;
            newNode->next = head;
            newNode->prev = tail;
            tail->next = newNode;
            head->prev = newNode;
        }
        size++;
        cout << "✓ Employee " << name << " (ID: " << id << ") added!" << endl;
    }
    
    Node* searchById(int id) {
        if (head == nullptr) return nullptr;
        Node* current = head;
        do {
            if (current->data.empId == id) return current;
            current = current->next;
        } while (current != head);
        return nullptr;
    }
    
    bool checkIn(int empId) {
        Node* empNode = searchById(empId);
        if (empNode == nullptr) {
            cout << "❌ Employee with ID " << empId << " not found!" << endl;
            return false;
        }
        if (empNode->data.isCheckedIn) {
            cout << "⚠️  Employee " << empNode->data.name << " already checked in!" << endl;
            return false;
        }
        empNode->data.isCheckedIn = true;
        empNode->data.checkInTime = getCurrentTime();
        empNode->data.checkInDate = getCurrentDate();
        cout << "✓ " << empNode->data.name << " checked in at " << empNode->data.checkInTime.toString() << endl;
        return true;
    }
    
    bool checkOut(int empId) {
        Node* empNode = searchById(empId);
        if (empNode == nullptr) {
            cout << "❌ Employee with ID " << empId << " not found!" << endl;
            return false;
        }
        if (!empNode->data.isCheckedIn) {
            cout << "⚠️  Employee " << empNode->data.name << " not checked in!" << endl;
            return false;
        }
        empNode->data.isCheckedIn = false;
        empNode->data.checkOutTime = getCurrentTime();
        empNode->data.checkOutDate = getCurrentDate();
        empNode->data.hoursWorked = calculateHours(empNode->data.checkInTime, empNode->data.checkOutTime);
        cout << "✓ " << empNode->data.name << " checked out. Hours: " << fixed << setprecision(2) << empNode->data.hoursWorked << endl;
        return true;
    }
    
    void displayAll() {
        if (head == nullptr) {
            cout << "📋 No employees in system!" << endl;
            return;
        }
        cout << "\n📋 === ALL EMPLOYEES ===" << endl;
        Node* current = head;
        do {
            current->data.displayInfo();
            current = current->next;
        } while (current != head);
        cout << "Total: " << size << " employees" << endl;
    }
    
    void sortById() {
        if (head == nullptr || size <= 1) return;
        bool swapped;
        do {
            swapped = false;
            Node* current = head;
            do {
                if (current->data.empId > current->next->data.empId) {
                    Employee temp = current->data;
                    current->data = current->next->data;
                    current->next->data = temp;
                    swapped = true;
                }
                current = current->next;
            } while (current->next != head);
        } while (swapped);
        cout << "✓ Sorted by ID" << endl;
    }
    
    void displayStatistics() {
        if (head == nullptr) {
            cout << "📊 No data available!" << endl;
            return;
        }
        int checkedIn = 0, checkedOut = 0;
        double totalHours = 0.0;
        Node* current = head;
        do {
            if (current->data.isCheckedIn) checkedIn++;
            else {
                checkedOut++;
                totalHours += current->data.hoursWorked;
            }
            current = current->next;
        } while (current != head);
        
        cout << "\n📊 === STATISTICS ===" << endl;
        cout << "Total employees: " << size << endl;
        cout << "Currently checked in: " << checkedIn << endl;
        cout << "Currently checked out: " << checkedOut << endl;
        cout << "Total hours worked: " << fixed << setprecision(2) << totalHours << endl;
    }
    
    void clear() {
        if (head == nullptr) return;
        Node* current = head;
        do {
            Node* next = current->next;
            delete current;
            current = next;
        } while (current != head);
        head = nullptr;
        size = 0;
    }
};

void runDemo() {
    cout << "\n🚀 === DOUBLY-CIRCULAR LINKED LIST DEMO ===" << endl;
    EmployeeAttendanceSystem system;
    
    cout << "\n1️⃣ Adding employees..." << endl;
    system.addEmployee(101, "Alice Johnson", "IT");
    system.addEmployee(102, "Bob Smith", "HR");
    system.addEmployee(103, "Carol Davis", "IT");
    system.addEmployee(104, "David Wilson", "Finance");
    
    system.displayAll();
    
    cout << "\n2️⃣ Check-in operations..." << endl;
    system.checkIn(101);
    system.checkIn(103);
    system.checkIn(104);
    
    cout << "\n3️⃣ Check-out operations..." << endl;
    system.checkOut(101);
    system.checkOut(103);
    
    cout << "\n4️⃣ Sorting by ID..." << endl;
    system.sortById();
    system.displayAll();
    
    cout << "\n5️⃣ Final statistics..." << endl;
    system.displayStatistics();
    
    cout << "\n✅ Demo completed! This demonstrates:" << endl;
    cout << "   • Doubly-circular linked list operations" << endl;
    cout << "   • Employee attendance management" << endl;
    cout << "   • Check-in/check-out with timestamps" << endl;
    cout << "   • Sorting algorithms" << endl;
    cout << "   • Statistical reporting" << endl;
}

int main() {
    cout << "🏢 ========================================" << endl;
    cout << "   EMPLOYEE ATTENDANCE SYSTEM" << endl;
    cout << "   (Doubly-Circular Linked List)" << endl;
    cout << "========================================" << endl;
    
    runDemo();
    return 0;
}
