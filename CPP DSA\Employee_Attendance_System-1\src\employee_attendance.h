#ifndef EMPLOYEE_ATTENDANCE_H
#define EMPLOYEE_ATTENDANCE_H

#include <iostream>
#include <string>
#include <ctime>

struct Employee {
    int id;
    std::string name;
    std::string department;
    std::time_t checkInTime;
    std::time_t checkOutTime;
    int hoursWorked;

    Employee(int empId, const std::string& empName, const std::string& empDepartment)
        : id(empId), name(empName), department(empDepartment), checkInTime(0), checkOutTime(0), hoursWorked(0) {}
};

class EmployeeAttendanceSystem {
private:
    struct Node {
        Employee data;
        Node* next;
        Node* prev;

        Node(const Employee& emp) : data(emp), next(nullptr), prev(nullptr) {}
    };

    Node* head;
    Node* tail;
    int size;

public:
    EmployeeAttendanceSystem();
    ~EmployeeAttendanceSystem();

    void addEmployee(int id, const std::string& name, const std::string& department);
    void removeEmployee(int id);
    void updateEmployee(int id, const std::string& name, const std::string& department);
    void checkIn(int id);
    void checkOut(int id);
    void displayEmployees() const;
    void generateReport() const;
};

#endif // EMPLOYEE_ATTENDANCE_H