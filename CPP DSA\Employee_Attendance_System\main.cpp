/*
================================================================================
                    EMPLOYEE ATTENDANCE SYSTEM - MAIN PROGRAM
================================================================================

GROUP MEMBERS:
1. Eden Yehualashet [IHRCS-352438-16]
2. <PERSON> [IHRCS-9781-16]
3. TEMESGEN ABEBE [IHRCS-328669-16]
4. MIHRETAB NIGATU [IHRCS-9599-16]
5. ERMIAS GIRMA [IHRCS-829949-16]

SUBMISSION DATE: June 04, 2025

PROGRAM DESCRIPTION:
This is the main driver program for the Employee Attendance Management System.
It provides a comprehensive menu-driven interface that allows users to:

1. Manage Employees: Add, remove, and update employee information
2. Track Attendance: Check-in and check-out operations with timestamps
3. Search & Filter: Find employees by ID, name, or department
4. Data Organization: Sort employees by various criteria
5. Reporting: View attendance statistics and summaries
6. Demo Mode: Automated demonstration of all system features

MENU SYSTEM:
- Interactive console-based interface
- Input validation and error handling
- Clear feedback for all operations
- Comprehensive demo mode for testing

TECHNICAL FEATURES:
- Uses doubly-circular linked list for efficient data management
- Real-time timestamp tracking for attendance
- Memory-safe operations with proper cleanup
- Modular design with separate header and implementation files

================================================================================
*/

#include "employee_attendance.h"
#include <iostream>
#include <thread> // For demo timing delays
#include <chrono> // For demo timing delays

using namespace std;

/**
 * @brief Display the main menu interface
 *
 * Shows all available operations in a formatted menu.
 * Provides clear numbering and descriptions for each option.
 */
void displayMenu()
{
    cout << "\n========================================" << endl;
    cout << "   EMPLOYEE ATTENDANCE SYSTEM" << endl;
    cout << "========================================" << endl;
    cout << "1.  Add Employee" << endl;
    cout << "2.  Remove Employee" << endl;
    cout << "3.  Check In Employee" << endl;
    cout << "4.  Check Out Employee" << endl;
    cout << "5.  Search Employee by ID" << endl;
    cout << "6.  Search Employee by Name" << endl;
    cout << "7.  Display All Employees" << endl;
    cout << "8.  Display Checked-In Employees" << endl;
    cout << "9.  Display Checked-Out Employees" << endl;
    cout << "10. Display Employees by Department" << endl;
    cout << "11. Update Employee Name" << endl;
    cout << "12. Update Employee Department" << endl;
    cout << "13. Sort by ID" << endl;
    cout << "14. Sort by Name" << endl;
    cout << "15. Sort by Hours Worked" << endl;
    cout << "16. Display Statistics" << endl;
    cout << "17. Run Demo Test" << endl;
    cout << "0.  Exit" << endl;
    cout << "========================================" << endl;
    cout << "Enter your choice: ";
}

/**
 * @brief Comprehensive demo function showcasing all system features
 *
 * This function demonstrates the complete functionality of the Employee
 * Attendance System using sample data based on the group members.
 * It includes timing delays to simulate real-world usage.
 *
 * DEMO FEATURES:
 * - Employee management (add, update, remove)
 * - Attendance tracking (check-in, check-out)
 * - Search operations (by ID, name, department)
 * - Sorting capabilities (by ID, name, hours)
 * - Statistical reporting
 *
 * @param system Reference to the EmployeeAttendanceSystem object
 */
void runDemoTest(EmployeeAttendanceSystem &system)
{
    cout << "\n=== RUNNING COMPREHENSIVE DEMO TEST ===" << endl;
    cout << "This demo uses our group members as sample employees" << endl;

    // Add sample employees based on group members
    cout << "\n--- Adding Group Members as Sample Employees ---" << endl;
    system.addEmployee(352438, "Eden Yehualashet", "IT");
    system.addEmployee(9781, "Abel Shiferaw", "HR");
    system.addEmployee(328669, "Temesgen Abebe", "IT");
    system.addEmployee(9599, "Mihretab Nigatu", "Finance");
    system.addEmployee(829949, "Ermias Girma", "HR");

    // Display all employees
    system.displayAll();

    // Check in some employees
    cout << "\n--- Check-in Operations ---" << endl;
    cout << "Checking in Eden, Temesgen, and Ermias..." << endl;
    system.checkIn(352438); // Eden
    this_thread::sleep_for(chrono::seconds(1));
    system.checkIn(328669); // Temesgen
    this_thread::sleep_for(chrono::seconds(1));
    system.checkIn(829949); // Ermias

    // Display checked-in employees
    system.displayCheckedInEmployees();

    // Wait a bit and check out some employees
    cout << "\n--- Waiting 3 seconds to simulate work time ---" << endl;
    this_thread::sleep_for(chrono::seconds(3));

    cout << "\n--- Check-out Operations ---" << endl;
    cout << "Checking out Eden and Temesgen..." << endl;
    system.checkOut(352438); // Eden
    this_thread::sleep_for(chrono::seconds(1));
    system.checkOut(328669); // Temesgen

    // Display checked-out employees
    system.displayCheckedOutEmployees();

    // Search operations
    cout << "\n--- Search Operations ---" << endl;
    Node *found = system.searchById(9781); // Abel
    if (found)
    {
        cout << "Found employee by ID 9781 (Abel): ";
        found->data.displayInfo();
    }

    found = system.searchByName("Ermias Girma");
    if (found)
    {
        cout << "Found employee by name 'Ermias Girma': ";
        found->data.displayInfo();
    }

    // Display by department
    cout << "\n--- Display by Department ---" << endl;
    system.displayEmployeesByDepartment("IT");

    // Update operations
    cout << "\n--- Update Operations ---" << endl;
    cout << "Updating Abel's name and Mihretab's department..." << endl;
    system.updateEmployeeName(9781, "Abel Shiferaw (Updated)");
    system.updateEmployeeDepartment(9599, "Accounting");

    // Sorting operations
    cout << "\n--- Sorting Operations ---" << endl;
    cout << "Sorting by name..." << endl;
    system.sortByName();
    system.displayAll();

    cout << "\nSorting by ID..." << endl;
    system.sortById();
    system.displayAll();

    // Check out remaining employee and sort by hours
    cout << "\nChecking out remaining employee (Ermias) and sorting by hours worked..." << endl;
    system.checkOut(829949); // Ermias
    system.sortByHoursWorked();
    system.displayAll();

    // Display statistics
    cout << "\n--- Final Statistics ---" << endl;
    system.displayStatistics();

    cout << "\n=== COMPREHENSIVE DEMO TEST COMPLETED ===" << endl;
    cout << "All features of the Employee Attendance System have been demonstrated!" << endl;
}

int main()
{
    EmployeeAttendanceSystem system;
    int choice;

    do
    {
        displayMenu();
        cin >> choice;

        switch (choice)
        {
        case 1:
        {
            int id;
            string name, department;
            cout << "Enter Employee ID: ";
            cin >> id;
            cin.ignore();
            cout << "Enter Employee Name: ";
            getline(cin, name);
            cout << "Enter Department: ";
            getline(cin, department);
            system.addEmployee(id, name, department);
            break;
        }
        case 2:
        {
            int id;
            cout << "Enter Employee ID to remove: ";
            cin >> id;
            system.removeEmployee(id);
            break;
        }
        case 3:
        {
            int id;
            cout << "Enter Employee ID to check in: ";
            cin >> id;
            system.checkIn(id);
            break;
        }
        case 4:
        {
            int id;
            cout << "Enter Employee ID to check out: ";
            cin >> id;
            system.checkOut(id);
            break;
        }
        case 5:
        {
            int id;
            cout << "Enter Employee ID to search: ";
            cin >> id;
            Node *found = system.searchById(id);
            if (found)
            {
                cout << "Employee found: ";
                found->data.displayInfo();
            }
            else
            {
                cout << "Employee not found!" << endl;
            }
            break;
        }
        case 6:
        {
            string name;
            cout << "Enter Employee Name to search: ";
            cin.ignore();
            getline(cin, name);
            Node *found = system.searchByName(name);
            if (found)
            {
                cout << "Employee found: ";
                found->data.displayInfo();
            }
            else
            {
                cout << "Employee not found!" << endl;
            }
            break;
        }
        case 7:
            system.displayAll();
            break;
        case 8:
            system.displayCheckedInEmployees();
            break;
        case 9:
            system.displayCheckedOutEmployees();
            break;
        case 10:
        {
            string department;
            cout << "Enter Department: ";
            cin.ignore();
            getline(cin, department);
            system.displayEmployeesByDepartment(department);
            break;
        }
        case 11:
        {
            int id;
            string newName;
            cout << "Enter Employee ID: ";
            cin >> id;
            cin.ignore();
            cout << "Enter New Name: ";
            getline(cin, newName);
            system.updateEmployeeName(id, newName);
            break;
        }
        case 12:
        {
            int id;
            string newDept;
            cout << "Enter Employee ID: ";
            cin >> id;
            cin.ignore();
            cout << "Enter New Department: ";
            getline(cin, newDept);
            system.updateEmployeeDepartment(id, newDept);
            break;
        }
        case 13:
            system.sortById();
            break;
        case 14:
            system.sortByName();
            break;
        case 15:
            system.sortByHoursWorked();
            break;
        case 16:
            system.displayStatistics();
            break;
        case 17:
            runDemoTest(system);
            break;
        case 0:
            cout << "Thank you for using Employee Attendance System!" << endl;
            break;
        default:
            cout << "Invalid choice! Please try again." << endl;
        }
    } while (choice != 0);

    return 0;
}
