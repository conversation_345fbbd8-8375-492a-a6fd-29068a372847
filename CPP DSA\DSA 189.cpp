#include <iostream>
using namespace std;

// Function to accept list from the user
void AcceptList(int List[], int &Size)
{
  cout << "Enter number of elements: ";
  cin >> Size;
  cout << "Enter " << Size << " elements: ";
  for (int i = 0; i < Size; i++)
    cin >> List[i];
}

// Linear Search function
int LinearSearch(int List[], int Size, int Key)
{
  for (int i = 0; i < Size; i++)
  {
    if (List[i] == Key)
      return i;
  }
  return -1;
}

// Binary Search function (requires sorted array)
int BinarySearch(int List[], int Size, int Key)
{
  int low = 0, high = Size - 1;
  while (low <= high)
  {
    int mid = (low + high) / 2;
    if (List[mid] == Key)
      return mid;
    else if (List[mid] < Key)
      low = mid + 1;
    else
      high = mid - 1;
  }
  return -1;
}

// Insertion Sort
void InsertSort(int List[], int Size)
{
  for (int i = 1; i < Size; i++)
  {
    int key = List[i], j = i - 1;
    while (j >= 0 && List[j] > key)
    {
      List[j + 1] = List[j];
      j--;
    }
    List[j + 1] = key;
  }
}

// Selection Sort
void SelectSort(int List[], int Size)
{
  for (int i = 0; i < Size - 1; i++)
  {
    int minIndex = i;
    for (int j = i + 1; j < Size; j++)
    {
      if (List[j] < List[minIndex])
        minIndex = j;
    }
    swap(List[i], List[minIndex]);
  }
}

// Bubble Sort
void BubbleSort(int List[], int Size)
{
  for (int i = 0; i < Size - 1; i++)
  {
    for (int j = 0; j < Size - i - 1; j++)
    {
      if (List[j] > List[j + 1])
        swap(List[j], List[j + 1]);
    }
  }
}

// ------------------- Linked List Section -------------------
struct Node
{
  int data;
  Node *next;
};

Node *head = nullptr;

void AddToStart(int value)
{
  Node *newNode = new Node{value, head};
  head = newNode;
}

void AddToEnd(int value)
{
  Node *newNode = new Node{value, nullptr};
  if (!head)
  {
    head = newNode;
    return;
  }
  Node *temp = head;
  while (temp->next)
    temp = temp->next;
  temp->next = newNode;
}

void RemoveFromStart()
{
  if (!head)
  {
    cout << "List is empty!\n";
    return;
  }
  Node *temp = head;
  head = head->next;
  delete temp;
}

void RemoveFromEnd()
{
  if (!head)
  {
    cout << "List is empty!\n";
    return;
  }
  if (!head->next)
  {
    delete head;
    head = nullptr;
    return;
  }
  Node *temp = head;
  while (temp->next->next)
    temp = temp->next;
  delete temp->next;
  temp->next = nullptr;
}

void TraverseList()
{
  Node *temp = head;
  cout << "Linked List: ";
  while (temp)
  {
    cout << temp->data << " ";
    temp = temp->next;
  }
  cout << endl;
}

// ------------------- Stack Section (Array Implementation) -------------------
const int MAX = 100;
int stack[MAX], top = -1;

void Push(int value)
{
  if (top == MAX - 1)
  {
    cout << "Stack Overflow!\n";
    return;
  }
  stack[++top] = value;
}

void Pop()
{
  if (top == -1)
  {
    cout << "Stack Underflow!\n";
    return;
  }
  cout << "Popped: " << stack[top--] << endl;
}

void DisplayStack()
{
  cout << "Stack: ";
  for (int i = top; i >= 0; i--)
    cout << stack[i] << " ";
  cout << endl;
}

// ------------------- Queue Section (Array Implementation) -------------------
int queue[MAX], front = -1, rear = -1;

void Enqueue(int value)
{
  if (rear == MAX - 1)
  {
    cout << "Queue Overflow!\n";
    return;
  }
  if (front == -1)
    front = 0;
  queue[++rear] = value;
}

void Dequeue()
{
  if (front == -1 || front > rear)
  {
    cout << "Queue Underflow!\n";
    front = rear = -1;
    return;
  }
  cout << "Dequeued: " << queue[front++] << endl;
}

void DisplayQueue()
{
  cout << "Queue: ";
  for (int i = front; i <= rear && front != -1; i++)
    cout << queue[i] << " ";
  cout << endl;
}

// ------------------- Menu -------------------
int Menu()
{
  int choice;
  cout << "\nMENU:\n1. Linear Search\n2. Binary Search\n3. Insert Sort\n4. Select Sort\n5. Bubble Sort\n6. Exit\nChoose an option: ";
  cin >> choice;
  return choice;
}

// ------------------- Main Function (Updated) -------------------
int main()
{
  int List[100], Size = 0, Key, choice, result;

  cout << "=== Data Structures and Algorithms Lab Exercises ===" << endl;
  cout << "Enter number of elements: ";
  cin >> Size;
  while (Size <= 0 || Size > 100)
  {
    cout << "Invalid size! Enter again: ";
    cin >> Size;
  }
  cout << "Enter " << Size << " elements: ";
  for (int i = 0; i < Size; i++)
    cin >> List[i];

  do
  {
    choice = Menu();
    switch (choice)
    {
    case 1:
      cout << "Enter element to search: ";
      cin >> Key;
      result = LinearSearch(List, Size, Key);
      if (result != -1)
        cout << "Element found at index " << result << endl;
      else
        cout << "Element not found" << endl;
      break;
    case 2:
      cout << "Sorting array first for binary search..." << endl;
      InsertSort(List, Size); // Use InsertSort to sort before binary search
      cout << "Sorted array: ";
      for (int i = 0; i < Size; i++)
        cout << List[i] << " ";
      cout << endl;
      cout << "Enter element to search: ";
      cin >> Key;
      result = BinarySearch(List, Size, Key);
      if (result != -1)
        cout << "Element found at index " << result << endl;
      else
        cout << "Element not found" << endl;
      break;
    case 3:
      InsertSort(List, Size);
      cout << "Array after Insertion Sort: ";
      for (int i = 0; i < Size; i++)
        cout << List[i] << " ";
      cout << endl;
      break;
    case 4:
      SelectSort(List, Size);
      cout << "Array after Selection Sort: ";
      for (int i = 0; i < Size; i++)
        cout << List[i] << " ";
      cout << endl;
      break;
    case 5:
      BubbleSort(List, Size);
      cout << "Array after Bubble Sort: ";
      for (int i = 0; i < Size; i++)
        cout << List[i] << " ";
      cout << endl;
      break;
    case 6:
      cout << "Exiting program. Thank you!" << endl;
      break;
    default:
      cout << "Invalid choice!\n";
    }
  } while (choice != 6);
  return 0;
}
