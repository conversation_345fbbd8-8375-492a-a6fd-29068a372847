/*
================================================================================
                    EMPLOYEE ATTENDANCE SYSTEM USING DOUBLY-C<PERSON><PERSON>LAR LINKED LIST
================================================================================

GROUP MEMBERS:
1. <PERSON>hualashet [IHRCS-352438-16]
2. <PERSON> [IHRCS-9781-16]
3. TEMESGEN ABEBE [IHRCS-328669-16]
4. MIHRETAB NIGATU [IHRCS-9599-16]
5. ERMIAS GIRMA [IHRCS-829949-16]

SUBMISSION DATE: June 04, 2025

PROJECT DESCRIPTION:
This project implements an Employee Attendance Management System using a
Doubly-Circular Linked List data structure. The system provides comprehensive
functionality for managing employee records and tracking their attendance.

KEY FEATURES:
- Add/Remove employees
- Check-in/Check-out operations with timestamp tracking
- Search employees by ID or name
- Sort employees by various criteria
- Display attendance statistics
- Update employee information
- Filter employees by department

DATA STRUCTURE: Doubly-Circular Linked List
- Each node contains employee data and pointers to next and previous nodes
- The last node points back to the first node (circular)
- Allows efficient bidirectional traversal
- Maintains O(1) insertion at head/tail

TIME COMPLEXITIES:
- Insertion: O(1) at head/tail, O(n) at arbitrary position
- Deletion: O(n) for search + O(1) for removal
- Search: O(n) linear search
- Sorting: O(n²) using bubble sort
- Display: O(n) for traversal

================================================================================
*/

#ifndef EMPLOYEE_ATTENDANCE_H
#define EMPLOYEE_ATTENDANCE_H

#include <iostream>
#include <string>
#include <ctime>
#include <iomanip>
#include <sstream>

using namespace std;

/*
================================================================================
                                UTILITY STRUCTURES
================================================================================
*/

/**
 * @brief Structure to represent time in 24-hour format
 *
 * This structure stores time information for check-in and check-out operations.
 * It provides functionality to format time as a readable string.
 *
 * @note Time is stored in 24-hour format (0-23 hours)
 */
struct Time
{
    int hour;   // Hour component (0-23)
    int minute; // Minute component (0-59)
    int second; // Second component (0-59)

    /**
     * @brief Default constructor - initializes time to 00:00:00
     */
    Time() : hour(0), minute(0), second(0) {}

    /**
     * @brief Parameterized constructor
     * @param h Hour value (0-23)
     * @param m Minute value (0-59)
     * @param s Second value (0-59)
     */
    Time(int h, int m, int s) : hour(h), minute(m), second(s) {}

    /**
     * @brief Converts time to formatted string (HH:MM:SS)
     * @return String representation of time in HH:MM:SS format
     */
    string toString() const
    {
        stringstream ss;
        ss << setfill('0') << setw(2) << hour << ":"
           << setfill('0') << setw(2) << minute << ":"
           << setfill('0') << setw(2) << second;
        return ss.str();
    }
};

/**
 * @brief Structure to represent date
 *
 * This structure stores date information for attendance tracking.
 * It provides functionality to format date as a readable string.
 */
struct Date
{
    int day;   // Day of the month (1-31)
    int month; // Month of the year (1-12)
    int year;  // Year (e.g., 2024, 2025)

    /**
     * @brief Default constructor - initializes to January 1, 2024
     */
    Date() : day(1), month(1), year(2024) {}

    /**
     * @brief Parameterized constructor
     * @param d Day value (1-31)
     * @param m Month value (1-12)
     * @param y Year value
     */
    Date(int d, int m, int y) : day(d), month(m), year(y) {}

    /**
     * @brief Converts date to formatted string (DD/MM/YYYY)
     * @return String representation of date in DD/MM/YYYY format
     */
    string toString() const
    {
        stringstream ss;
        ss << setfill('0') << setw(2) << day << "/"
           << setfill('0') << setw(2) << month << "/"
           << year;
        return ss.str();
    }
};

/*
================================================================================
                                CORE DATA STRUCTURES
================================================================================
*/

/**
 * @brief Employee structure to store employee information and attendance data
 *
 * This structure contains all the necessary information about an employee
 * including personal details, attendance status, and work hours tracking.
 */
struct Employee
{
    // Employee identification and personal information
    int empId;         // Unique employee ID number
    string name;       // Employee's full name
    string department; // Department where employee works

    // Attendance tracking information
    Date checkInDate;   // Date when employee checked in
    Time checkInTime;   // Time when employee checked in
    Date checkOutDate;  // Date when employee checked out
    Time checkOutTime;  // Time when employee checked out
    bool isCheckedIn;   // Current attendance status (true = checked in, false = checked out)
    double hoursWorked; // Total hours worked in current session

    /**
     * @brief Default constructor - initializes employee with default values
     */
    Employee() : empId(0), name(""), department(""), isCheckedIn(false), hoursWorked(0.0) {}

    /**
     * @brief Parameterized constructor to create employee with basic information
     * @param id Unique employee ID
     * @param n Employee's name
     * @param dept Employee's department
     */
    Employee(int id, string n, string dept)
        : empId(id), name(n), department(dept), isCheckedIn(false), hoursWorked(0.0) {}

    /**
     * @brief Display employee information in formatted output
     *
     * Shows employee ID, name, department, and current attendance status.
     * If checked in, shows check-in time and date.
     * If checked out, shows hours worked (if any).
     */
    void displayInfo() const
    {
        cout << "ID: " << empId << ", Name: " << name
             << ", Department: " << department;
        if (isCheckedIn)
        {
            cout << " [CHECKED IN at " << checkInTime.toString()
                 << " on " << checkInDate.toString() << "]";
        }
        else
        {
            cout << " [CHECKED OUT]";
            if (hoursWorked > 0)
            {
                cout << " - Hours worked: " << fixed << setprecision(2) << hoursWorked;
            }
        }
        cout << endl;
    }
};

/**
 * @brief Node structure for doubly-circular linked list implementation
 *
 * Each node contains:
 * - Employee data
 * - Pointer to next node (circular: last node points to first)
 * - Pointer to previous node (circular: first node's prev points to last)
 *
 * This structure enables efficient bidirectional traversal and maintains
 * the circular property of the linked list.
 */
struct Node
{
    Employee data; // Employee information stored in this node
    Node *next;    // Pointer to the next node in the list
    Node *prev;    // Pointer to the previous node in the list

    /**
     * @brief Constructor to create a new node with employee data
     * @param emp Employee object to store in this node
     */
    Node(const Employee &emp) : data(emp), next(nullptr), prev(nullptr) {}
};

/*
================================================================================
                        EMPLOYEE ATTENDANCE SYSTEM CLASS
================================================================================
*/

/**
 * @brief Employee Attendance Management System using Doubly-Circular Linked List
 *
 * This class implements a comprehensive employee attendance tracking system using
 * a doubly-circular linked list data structure. The system provides functionality
 * for managing employee records, tracking attendance, and generating reports.
 *
 * KEY FEATURES:
 * - Employee management (add, remove, update)
 * - Attendance tracking (check-in, check-out with timestamps)
 * - Search functionality (by ID, name, department)
 * - Sorting capabilities (by ID, name, hours worked)
 * - Statistical reporting
 *
 * DATA STRUCTURE PROPERTIES:
 * - Doubly-linked: Each node has pointers to both next and previous nodes
 * - Circular: Last node points back to first node, first node's prev points to last
 * - Dynamic: Size can grow/shrink during runtime
 * - Memory efficient: Only allocates memory as needed
 *
 * TIME COMPLEXITIES:
 * - Insertion at head/tail: O(1)
 * - Search operations: O(n)
 * - Deletion: O(n) for search + O(1) for removal
 * - Sorting: O(n²) using bubble sort
 * - Display operations: O(n)
 */
class EmployeeAttendanceSystem
{
private:
    Node *head; // Pointer to the first node in the circular list
    int size;   // Current number of employees in the system

    /**
     * @brief Helper function to get current system time
     * @return Time object containing current hour, minute, and second
     *
     * Uses system time functions to get real-time timestamp for attendance tracking.
     */
    Time getCurrentTime()
    {
        time_t now = time(0);
        tm *ltm = localtime(&now);
        return Time(ltm->tm_hour, ltm->tm_min, ltm->tm_sec);
    }

    /**
     * @brief Helper function to get current system date
     * @return Date object containing current day, month, and year
     *
     * Uses system date functions to get real-time date for attendance tracking.
     */
    Date getCurrentDate()
    {
        time_t now = time(0);
        tm *ltm = localtime(&now);
        return Date(ltm->tm_mday, 1 + ltm->tm_mon, 1900 + ltm->tm_year);
    }

    /**
     * @brief Helper function to calculate hours worked between check-in and check-out
     * @param checkIn Time when employee checked in
     * @param checkOut Time when employee checked out
     * @return Double value representing hours worked (with decimal precision)
     *
     * Handles cases where check-out time is on the next day (after midnight).
     * Converts time to minutes for calculation, then back to hours.
     */
    double calculateHours(const Time &checkIn, const Time &checkOut)
    {
        int totalMinutesIn = checkIn.hour * 60 + checkIn.minute;
        int totalMinutesOut = checkOut.hour * 60 + checkOut.minute;

        // Handle case where checkout is next day (after midnight)
        if (totalMinutesOut < totalMinutesIn)
        {
            totalMinutesOut += 24 * 60; // Add 24 hours worth of minutes
        }

        return (totalMinutesOut - totalMinutesIn) / 60.0; // Convert back to hours
    }

public:
    /**
     * @brief Default constructor - initializes empty attendance system
     *
     * Creates an empty doubly-circular linked list with head pointer set to nullptr
     * and size initialized to 0.
     */
    EmployeeAttendanceSystem() : head(nullptr), size(0) {}

    /**
     * @brief Destructor - cleans up all allocated memory
     *
     * Automatically called when object goes out of scope.
     * Calls clear() to deallocate all nodes and prevent memory leaks.
     */
    ~EmployeeAttendanceSystem()
    {
        clear();
    }

    /*
    ============================================================================
                                BASIC OPERATIONS
    ============================================================================
    */

    /**
     * @brief Add a new employee to the system
     * @param id Unique employee ID (must be positive and unique)
     * @param name Employee's full name
     * @param department Employee's department
     *
     * Time Complexity: O(n) for duplicate check + O(1) for insertion = O(n)
     * Space Complexity: O(1)
     *
     * Adds employee at the end of the circular list (before head).
     * Checks for duplicate IDs before insertion.
     */
    void addEmployee(int id, const string &name, const string &department);

    /**
     * @brief Remove an employee from the system by ID
     * @param id Employee ID to remove
     * @return true if employee was found and removed, false otherwise
     *
     * Time Complexity: O(n) for search + O(1) for removal = O(n)
     * Space Complexity: O(1)
     *
     * Maintains circular list properties after removal.
     */
    bool removeEmployee(int id);

    /**
     * @brief Clear all employees from the system
     *
     * Time Complexity: O(n)
     * Space Complexity: O(1)
     *
     * Deallocates all nodes and resets head to nullptr, size to 0.
     * Used by destructor to prevent memory leaks.
     */
    void clear();

    /**
     * @brief Check if the system is empty
     * @return true if no employees in system, false otherwise
     *
     * Time Complexity: O(1)
     */
    bool isEmpty() const { return head == nullptr; }

    /**
     * @brief Get the current number of employees in the system
     * @return Integer count of employees
     *
     * Time Complexity: O(1)
     */
    int getSize() const { return size; }

    /*
    ============================================================================
                            ATTENDANCE OPERATIONS
    ============================================================================
    */

    /**
     * @brief Check in an employee by ID
     * @param empId Employee ID to check in
     * @return true if check-in successful, false if employee not found or already checked in
     *
     * Time Complexity: O(n) for search
     *
     * Records current date and time as check-in timestamp.
     * Prevents double check-in for same employee.
     */
    bool checkIn(int empId);

    /**
     * @brief Check out an employee by ID
     * @param empId Employee ID to check out
     * @return true if check-out successful, false if employee not found or not checked in
     *
     * Time Complexity: O(n) for search
     *
     * Records current date and time as check-out timestamp.
     * Calculates and stores total hours worked.
     */
    bool checkOut(int empId);

    /*
    ============================================================================
                              SEARCH OPERATIONS
    ============================================================================
    */

    /**
     * @brief Search for an employee by ID
     * @param id Employee ID to search for
     * @return Pointer to node containing employee, or nullptr if not found
     *
     * Time Complexity: O(n) - linear search through circular list
     */
    Node *searchById(int id);

    /**
     * @brief Search for an employee by name
     * @param name Employee name to search for (exact match)
     * @return Pointer to node containing employee, or nullptr if not found
     *
     * Time Complexity: O(n) - linear search through circular list
     */
    Node *searchByName(const string &name);

    /**
     * @brief Display all employees in a specific department
     * @param department Department name to filter by
     *
     * Time Complexity: O(n) - traverses entire list once
     *
     * Shows all employees belonging to the specified department.
     */
    void displayEmployeesByDepartment(const string &department);

    /*
    ============================================================================
                              UPDATE OPERATIONS
    ============================================================================
    */

    /**
     * @brief Update an employee's name
     * @param id Employee ID to update
     * @param newName New name for the employee
     * @return true if update successful, false if employee not found
     *
     * Time Complexity: O(n) for search + O(1) for update = O(n)
     */
    bool updateEmployeeName(int id, const string &newName);

    /**
     * @brief Update an employee's department
     * @param id Employee ID to update
     * @param newDepartment New department for the employee
     * @return true if update successful, false if employee not found
     *
     * Time Complexity: O(n) for search + O(1) for update = O(n)
     */
    bool updateEmployeeDepartment(int id, const string &newDepartment);

    /*
    ============================================================================
                              DISPLAY OPERATIONS
    ============================================================================
    */

    /**
     * @brief Display all employees in the system
     *
     * Time Complexity: O(n)
     *
     * Traverses the circular list once and displays each employee's information.
     */
    void displayAll();

    /**
     * @brief Display only employees who are currently checked in
     *
     * Time Complexity: O(n)
     *
     * Filters and displays employees with isCheckedIn = true.
     */
    void displayCheckedInEmployees();

    /**
     * @brief Display only employees who are currently checked out
     *
     * Time Complexity: O(n)
     *
     * Filters and displays employees with isCheckedIn = false.
     */
    void displayCheckedOutEmployees();

    /*
    ============================================================================
                              SORTING OPERATIONS
    ============================================================================
    */

    /**
     * @brief Sort employees by ID in ascending order
     *
     * Time Complexity: O(n²) - uses bubble sort algorithm
     * Space Complexity: O(1) - in-place sorting
     *
     * Sorts by swapping employee data within nodes (not node pointers).
     */
    void sortById();

    /**
     * @brief Sort employees by name in alphabetical order
     *
     * Time Complexity: O(n²) - uses bubble sort algorithm
     * Space Complexity: O(1) - in-place sorting
     *
     * Uses string comparison for alphabetical ordering.
     */
    void sortByName();

    /**
     * @brief Sort employees by hours worked in descending order
     *
     * Time Complexity: O(n²) - uses bubble sort algorithm
     * Space Complexity: O(1) - in-place sorting
     *
     * Employees with more hours worked appear first.
     */
    void sortByHoursWorked();

    /*
    ============================================================================
                              UTILITY FUNCTIONS
    ============================================================================
    */

    /**
     * @brief Display comprehensive attendance statistics
     *
     * Time Complexity: O(n)
     *
     * Shows:
     * - Total number of employees
     * - Number of employees currently checked in
     * - Number of employees currently checked out
     * - Total hours worked by all employees
     * - Average hours worked per employee
     */
    void displayStatistics();
};

#endif
