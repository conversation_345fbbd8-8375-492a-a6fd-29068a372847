# Compilation Guide for Employee Attendance System

## Requirements
- C++ compiler (g++, clang++, or MSVC)
- C++11 standard or later
- Standard libraries: iostream, string, ctime, algorithm

## Compilation Instructions

### Method 1: Direct Compilation
To compile the project directly from the command line, navigate to the project directory and run:
```
g++ src/main.cpp src/employee_attendance.cpp -o employee_attendance
```

### Method 2: Using Makefile
If you have a Makefile in the project directory, you can compile the project by simply running:
```
make
```

### Method 3: With Additional Flags
For more detailed compilation with warnings, use the following command:
```
g++ -std=c++11 -Wall -Wextra src/main.cpp src/employee_attendance.cpp -o employee_attendance
```

## Execution Instructions
After successful compilation, you can run the program using:
```
./employee_attendance    # Linux/Mac
employee_attendance.exe  # Windows
```

## Notes
- Ensure that all source files are in the `src` directory.
- If you encounter any issues, check for missing dependencies or incorrect paths.